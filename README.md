# CS内战分队小程序

一个专为CS:GO内战设计的微信小程序，提供智能分队和BP ban图功能。

## 功能特色

### 🎯 核心功能
- **智能分队系统** - A队 vs B队，支持最多5v5分队
- **BP Ban图模式** - 7张经典地图的专业BP流程
- **多用户实时交互** - 支持多人同时在线操作
- **详细记录统计** - 完整的BP记录和数据统计

### 🗺️ 支持地图
- 沙二 (Dust2)
- 小镇 (Mirage) 
- 迷城 (Inferno)
- 火车 (Train)
- 遗迹 (Ancient)
- 核子 (Nuke)
- 游乐园 (Vertigo)

### 📊 BP流程
1. A队 BAN 一张地图
2. B队 BAN 一张地图
3. A队 选择一张地图
4. B队 选择一张地图
5. A队 BAN 一张地图
6. B队 BAN 一张地图
7. 最后一张地图自动选择

## 页面结构

### 首页 (index)
- 功能导航
- 快速开始
- 今日统计
- 用户信息

### 分队页面 (team)
- A队/B队分组
- 在线用户列表
- 队伍管理
- 开始BP按钮

### BP Ban图页面 (ban-pick)
- 地图网格显示
- BP流程指引
- 实时计时器
- 操作记录

### 历史记录页面 (records)
- BP记录列表
- 筛选功能（全部/今日/本周/本月）
- 统计信息
- 记录分享和删除

### 个人中心页面 (profile)
- 用户信息管理
- 个人统计数据
- 设置选项
- 数据管理

## 技术特点

### 前端技术
- 微信小程序原生开发
- JavaScript ES6+
- LESS样式预处理
- 响应式设计

### 数据存储
- 微信小程序本地存储
- 实时数据同步
- 历史记录管理

### 用户体验
- 流畅的动画效果
- 震动和音效反馈
- 直观的操作界面
- 完善的错误提示

## 安装和运行

1. 下载微信开发者工具
2. 导入项目目录
3. 配置小程序AppID
4. 编译运行

## 项目结构

```
miniprogram/
├── pages/              # 页面文件
│   ├── index/         # 首页
│   ├── team/          # 分队页面
│   ├── ban-pick/      # BP页面
│   ├── records/       # 记录页面
│   └── profile/       # 个人中心
├── images/            # 图片资源
├── app.js            # 应用入口
├── app.json          # 应用配置
└── app.less          # 全局样式
```

## 使用说明

### 开始分队
1. 进入分队页面
2. 点击"加入A队"或"加入B队"
3. 等待其他玩家加入
4. 点击"开始BP"

### BP Ban图
1. 按照流程提示操作
2. 点击地图进行BAN或选择
3. 查看实时BP进度
4. 完成后自动保存记录

### 查看记录
1. 进入历史记录页面
2. 使用筛选功能查看特定时间段
3. 点击记录查看详情
4. 支持分享和删除操作

## 更新日志

### v1.0.0
- 基础分队功能
- BP ban图系统
- 记录统计功能
- 用户管理系统

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 微信小程序内反馈

---

**享受你的CS内战！** 🎮
