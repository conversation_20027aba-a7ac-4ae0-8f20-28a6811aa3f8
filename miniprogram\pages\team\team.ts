// pages/team/team.ts

interface User {
  id: string;
  nickname: string;
  avatar: string;
  team?: 'A' | 'B';
}

interface TeamData {
  teamA: User[];
  teamB: User[];
  onlineUsers: User[];
  currentUser: User;
  canStartGame: boolean;
}

Page<TeamData>({
  data: {
    teamA: [],
    teamB: [],
    onlineUsers: [],
    currentUser: {
      id: '',
      nickname: '',
      avatar: ''
    },
    canStartGame: false
  },

  onLoad() {
    this.initUser();
    this.loadOnlineUsers();
    this.setupRealTimeUpdates();
  },

  // 初始化当前用户
  initUser() {
    // 获取微信用户信息
    wx.getUserProfile({
      desc: '用于显示用户信息',
      success: (res) => {
        const userInfo = res.userInfo;
        const currentUser = {
          id: wx.getStorageSync('userId') || this.generateUserId(),
          nickname: userInfo.nickName,
          avatar: userInfo.avatarUrl
        };
        
        this.setData({ currentUser });
        wx.setStorageSync('userId', currentUser.id);
        wx.setStorageSync('userInfo', currentUser);
        
        // 将用户添加到在线用户列表
        this.addToOnlineUsers(currentUser);
      },
      fail: () => {
        // 使用默认用户信息
        const currentUser = {
          id: wx.getStorageSync('userId') || this.generateUserId(),
          nickname: '用户' + Math.floor(Math.random() * 1000),
          avatar: '/images/default-avatar.png'
        };
        
        this.setData({ currentUser });
        wx.setStorageSync('userId', currentUser.id);
        this.addToOnlineUsers(currentUser);
      }
    });
  },

  // 生成用户ID
  generateUserId(): string {
    return 'user_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
  },

  // 加载在线用户
  loadOnlineUsers() {
    // 模拟从服务器获取在线用户
    const mockUsers: User[] = [
      {
        id: 'user1',
        nickname: '玩家1',
        avatar: '/images/avatar1.png'
      },
      {
        id: 'user2', 
        nickname: '玩家2',
        avatar: '/images/avatar2.png'
      }
    ];
    
    this.setData({
      onlineUsers: mockUsers
    });
  },

  // 添加到在线用户列表
  addToOnlineUsers(user: User) {
    const { onlineUsers } = this.data;
    const existingIndex = onlineUsers.findIndex(u => u.id === user.id);
    
    if (existingIndex === -1) {
      onlineUsers.push(user);
      this.setData({ onlineUsers });
    }
  },

  // 加入A队
  joinTeamA() {
    const { currentUser, teamA, teamB } = this.data;
    
    if (teamA.length >= 5) {
      wx.showToast({
        title: 'A队已满',
        icon: 'none'
      });
      return;
    }

    // 从B队移除（如果在B队）
    const newTeamB = teamB.filter(user => user.id !== currentUser.id);
    
    // 添加到A队（如果不在A队）
    const isInTeamA = teamA.some(user => user.id === currentUser.id);
    if (!isInTeamA) {
      const newTeamA = [...teamA, { ...currentUser, team: 'A' }];
      this.setData({
        teamA: newTeamA,
        teamB: newTeamB
      });
      this.updateCanStartGame();
      this.syncTeamData();
    }
  },

  // 加入B队
  joinTeamB() {
    const { currentUser, teamA, teamB } = this.data;
    
    if (teamB.length >= 5) {
      wx.showToast({
        title: 'B队已满',
        icon: 'none'
      });
      return;
    }

    // 从A队移除（如果在A队）
    const newTeamA = teamA.filter(user => user.id !== currentUser.id);
    
    // 添加到B队（如果不在B队）
    const isInTeamB = teamB.some(user => user.id === currentUser.id);
    if (!isInTeamB) {
      const newTeamB = [...teamB, { ...currentUser, team: 'B' }];
      this.setData({
        teamA: newTeamA,
        teamB: newTeamB
      });
      this.updateCanStartGame();
      this.syncTeamData();
    }
  },

  // 离开队伍
  leaveTeam() {
    const { currentUser, teamA, teamB } = this.data;
    
    const newTeamA = teamA.filter(user => user.id !== currentUser.id);
    const newTeamB = teamB.filter(user => user.id !== currentUser.id);
    
    this.setData({
      teamA: newTeamA,
      teamB: newTeamB
    });
    
    this.updateCanStartGame();
    this.syncTeamData();
  },

  // 重置分队
  resetTeams() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有分队吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            teamA: [],
            teamB: [],
            canStartGame: false
          });
          this.syncTeamData();
        }
      }
    });
  },

  // 开始BP
  startBanPick() {
    if (!this.data.canStartGame) {
      wx.showToast({
        title: '两队人数不足',
        icon: 'none'
      });
      return;
    }

    // 保存当前分队信息
    wx.setStorageSync('currentTeams', {
      teamA: this.data.teamA,
      teamB: this.data.teamB
    });

    // 跳转到BP页面
    wx.navigateTo({
      url: '/pages/ban-pick/ban-pick'
    });
  },

  // 更新是否可以开始游戏
  updateCanStartGame() {
    const { teamA, teamB } = this.data;
    const canStart = teamA.length > 0 && teamB.length > 0;
    this.setData({ canStartGame: canStart });
  },

  // 同步队伍数据（实际项目中应该同步到服务器）
  syncTeamData() {
    const { teamA, teamB } = this.data;
    wx.setStorageSync('teamData', { teamA, teamB });
  },

  // 设置实时更新（模拟）
  setupRealTimeUpdates() {
    // 在实际项目中，这里应该建立WebSocket连接
    // 定期检查队伍变化
    setInterval(() => {
      const savedTeamData = wx.getStorageSync('teamData');
      if (savedTeamData) {
        this.setData({
          teamA: savedTeamData.teamA || [],
          teamB: savedTeamData.teamB || []
        });
        this.updateCanStartGame();
      }
    }, 2000);
  }
});
