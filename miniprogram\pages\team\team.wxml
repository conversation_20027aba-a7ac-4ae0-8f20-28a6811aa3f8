<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">CS内战分队</text>
    <text class="subtitle">选择你的队伍</text>
  </view>

  <!-- 分队区域 -->
  <view class="teams-container">
    <!-- A队 -->
    <view class="team-section team-a">
      <view class="team-header">
        <text class="team-name">A队</text>
        <text class="team-count">{{teamA.length}}/5</text>
      </view>
      <view class="team-members">
        <view wx:for="{{teamA}}" wx:key="id" class="member-item">
          <image class="avatar" src="{{item.avatar}}" />
          <text class="nickname">{{item.nickname}}</text>
          <view wx:if="{{item.id === currentUser.id}}" class="leave-btn" bindtap="leaveTeam">
            离队
          </view>
        </view>
        <view wx:if="{{teamA.length < 5}}" class="join-btn" bindtap="joinTeamA">
          <text>加入A队</text>
        </view>
      </view>
    </view>

    <!-- VS 分隔符 -->
    <view class="vs-divider">
      <text>VS</text>
    </view>

    <!-- B队 -->
    <view class="team-section team-b">
      <view class="team-header">
        <text class="team-name">B队</text>
        <text class="team-count">{{teamB.length}}/5</text>
      </view>
      <view class="team-members">
        <view wx:for="{{teamB}}" wx:key="id" class="member-item">
          <image class="avatar" src="{{item.avatar}}" />
          <text class="nickname">{{item.nickname}}</text>
          <view wx:if="{{item.id === currentUser.id}}" class="leave-btn" bindtap="leaveTeam">
            离队
          </view>
        </view>
        <view wx:if="{{teamB.length < 5}}" class="join-btn" bindtap="joinTeamB">
          <text>加入B队</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn reset-btn" bindtap="resetTeams">重置分队</button>
    <button class="action-btn start-btn" bindtap="startBanPick" disabled="{{!canStartGame}}">
      开始BP
    </button>
  </view>

  <!-- 在线用户列表 -->
  <view class="online-users">
    <text class="section-title">在线用户 ({{onlineUsers.length}})</text>
    <scroll-view class="users-list" scroll-y>
      <view wx:for="{{onlineUsers}}" wx:key="id" class="user-item">
        <image class="avatar" src="{{item.avatar}}" />
        <text class="nickname">{{item.nickname}}</text>
        <text class="status">{{item.team || '未分队'}}</text>
      </view>
    </scroll-view>
  </view>
</view>
