// pages/ban-pick/ban-pick.ts

interface Map {
  id: string;
  name: string;
  image: string;
  status: 'available' | 'banned-a' | 'banned-b' | 'picked-a' | 'picked-b';
}

interface BPStep {
  text: string;
  team: 'A' | 'B';
  action: 'ban' | 'pick';
  completed: boolean;
  current: boolean;
  mapName?: string;
}

interface BanPickData {
  maps: Map[];
  bpProcess: BPStep[];
  currentStep: number;
  currentRoundText: string;
  remainingTime: number;
  showTimer: boolean;
  scoreA: number;
  scoreB: number;
  canFinish: boolean;
}

Page<BanPickData>({
  data: {
    maps: [
      { id: 'dust2', name: '沙二', image: '/images/maps/dust2.jpg', status: 'available' },
      { id: 'mirage', name: '小镇', image: '/images/maps/mirage.jpg', status: 'available' },
      { id: 'inferno', name: '迷城', image: '/images/maps/inferno.jpg', status: 'available' },
      { id: 'train', name: '火车', image: '/images/maps/train.jpg', status: 'available' },
      { id: 'ancient', name: '遗迹', image: '/images/maps/ancient.jpg', status: 'available' },
      { id: 'nuke', name: '核子', image: '/images/maps/nuke.jpg', status: 'available' },
      { id: 'vertigo', name: '游乐园', image: '/images/maps/vertigo.jpg', status: 'available' }
    ],
    bpProcess: [
      { text: 'A队 BAN 一张地图', team: 'A', action: 'ban', completed: false, current: true },
      { text: 'B队 BAN 一张地图', team: 'B', action: 'ban', completed: false, current: false },
      { text: 'A队 选择一张地图', team: 'A', action: 'pick', completed: false, current: false },
      { text: 'B队 选择一张地图', team: 'B', action: 'pick', completed: false, current: false },
      { text: 'A队 BAN 一张地图', team: 'A', action: 'ban', completed: false, current: false },
      { text: 'B队 BAN 一张地图', team: 'B', action: 'ban', completed: false, current: false },
      { text: '最后一张地图自动选择', team: 'A', action: 'pick', completed: false, current: false }
    ],
    currentStep: 0,
    currentRoundText: 'A队 BAN 一张地图',
    remainingTime: 30,
    showTimer: true,
    scoreA: 0,
    scoreB: 0,
    canFinish: false
  },

  timer: null as any,

  onLoad() {
    this.loadTeamData();
    this.startTimer();
  },

  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },

  // 加载队伍数据
  loadTeamData() {
    const teamData = wx.getStorageSync('currentTeams');
    if (teamData) {
      console.log('Team data loaded:', teamData);
    }
  },

  // 开始计时器
  startTimer() {
    this.timer = setInterval(() => {
      const { remainingTime } = this.data;
      if (remainingTime > 0) {
        this.setData({
          remainingTime: remainingTime - 1
        });
      } else {
        this.autoSkipStep();
      }
    }, 1000);
  },

  // 自动跳过步骤
  autoSkipStep() {
    wx.showToast({
      title: '时间到，自动跳过',
      icon: 'none'
    });
    this.nextStep();
  },

  // 处理地图点击
  handleMapClick(e: any) {
    const mapId = e.currentTarget.dataset.mapId;
    const { maps, currentStep, bpProcess } = this.data;
    
    const map = maps.find(m => m.id === mapId);
    const currentBPStep = bpProcess[currentStep];
    
    if (!map || map.status !== 'available' || !currentBPStep) {
      return;
    }

    // 更新地图状态
    const newMaps = maps.map(m => {
      if (m.id === mapId) {
        if (currentBPStep.action === 'ban') {
          m.status = currentBPStep.team === 'A' ? 'banned-a' : 'banned-b';
        } else {
          m.status = currentBPStep.team === 'A' ? 'picked-a' : 'picked-b';
        }
      }
      return m;
    });

    // 更新BP流程
    const newBPProcess = bpProcess.map((step, index) => {
      if (index === currentStep) {
        step.completed = true;
        step.current = false;
        step.mapName = map.name;
      } else if (index === currentStep + 1) {
        step.current = true;
      }
      return step;
    });

    this.setData({
      maps: newMaps,
      bpProcess: newBPProcess
    });

    this.nextStep();
  },

  // 进入下一步
  nextStep() {
    const { currentStep, bpProcess } = this.data;
    const nextStep = currentStep + 1;

    if (nextStep >= bpProcess.length) {
      // BP完成，处理最后一张地图
      this.handleFinalMap();
      return;
    }

    const nextBPStep = bpProcess[nextStep];
    this.setData({
      currentStep: nextStep,
      currentRoundText: nextBPStep.text,
      remainingTime: 30
    });
  },

  // 处理最后一张地图
  handleFinalMap() {
    const { maps } = this.data;
    const finalMap = maps.find(m => m.status === 'available');
    
    if (finalMap) {
      const newMaps = maps.map(m => {
        if (m.id === finalMap.id) {
          m.status = 'picked-a'; // 默认给A队
        }
        return m;
      });

      const newBPProcess = this.data.bpProcess.map((step, index) => {
        if (index === this.data.bpProcess.length - 1) {
          step.completed = true;
          step.current = false;
          step.mapName = finalMap.name;
        }
        return step;
      });

      this.setData({
        maps: newMaps,
        bpProcess: newBPProcess,
        currentRoundText: 'BP完成！',
        showTimer: false,
        canFinish: true
      });

      if (this.timer) {
        clearInterval(this.timer);
      }
    }
  },

  // 重置BP
  resetBP() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置BP流程吗？',
      success: (res) => {
        if (res.confirm) {
          // 重置所有数据
          const resetMaps = this.data.maps.map(m => ({
            ...m,
            status: 'available' as const
          }));

          const resetBPProcess = this.data.bpProcess.map((step, index) => ({
            ...step,
            completed: false,
            current: index === 0,
            mapName: undefined
          }));

          this.setData({
            maps: resetMaps,
            bpProcess: resetBPProcess,
            currentStep: 0,
            currentRoundText: 'A队 BAN 一张地图',
            remainingTime: 30,
            showTimer: true,
            canFinish: false
          });

          this.startTimer();
        }
      }
    });
  },

  // 完成BP
  finishBP() {
    const { maps, bpProcess } = this.data;
    
    // 保存BP记录
    const bpRecord = {
      timestamp: Date.now(),
      maps: maps,
      process: bpProcess,
      teamA: wx.getStorageSync('currentTeams')?.teamA || [],
      teamB: wx.getStorageSync('currentTeams')?.teamB || []
    };

    // 获取历史记录
    const records = wx.getStorageSync('bpRecords') || [];
    records.unshift(bpRecord);
    
    // 只保留最近50条记录
    if (records.length > 50) {
      records.splice(50);
    }
    
    wx.setStorageSync('bpRecords', records);

    wx.showToast({
      title: 'BP完成，已保存记录',
      icon: 'success'
    });

    // 跳转到记录页面
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/records/records'
      });
    }, 1500);
  }
});
