// index.js

Page({
  data: {
    userInfo: {},
    todayStats: {
      games: 0,
      users: 0,
      maps: 0
    }
  },

  onLoad() {
    this.loadUserInfo();
    this.loadTodayStats();
  },

  onShow() {
    this.loadTodayStats();
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
    }
  },

  // 加载今日统计
  loadTodayStats() {
    const records = wx.getStorageSync('bpRecords') || [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // 今日记录
    const todayRecords = records.filter(record => {
      const recordDate = new Date(record.timestamp);
      recordDate.setHours(0, 0, 0, 0);
      return recordDate.getTime() === today.getTime();
    });

    // 统计今日使用的地图数量
    const todayMaps = new Set();
    todayRecords.forEach(record => {
      record.maps.forEach(map => {
        if (map.status.includes('picked')) {
          todayMaps.add(map.name);
        }
      });
    });

    this.setData({
      todayStats: {
        games: todayRecords.length,
        users: Math.max(todayRecords.length * 2, 0), // 假设每场游戏至少2个用户
        maps: todayMaps.size
      }
    });
  },

  // 跳转到分队页面
  goToTeam() {
    wx.navigateTo({
      url: '/pages/team/team'
    });
  },

  // 跳转到BP页面
  goToBanPick() {
    // 检查是否有分队数据
    const teamData = wx.getStorageSync('currentTeams');
    if (teamData && teamData.teamA && teamData.teamB && 
        teamData.teamA.length > 0 && teamData.teamB.length > 0) {
      wx.navigateTo({
        url: '/pages/ban-pick/ban-pick'
      });
    } else {
      wx.showModal({
        title: '提示',
        content: '请先进行分队再开始BP',
        showCancel: true,
        confirmText: '去分队',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/team/team'
            });
          }
        }
      });
    }
  },

  // 跳转到记录页面
  goToRecords() {
    wx.navigateTo({
      url: '/pages/records/records'
    });
  },

  // 跳转到个人中心
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'CS内战分队小程序',
      desc: '智能分队，BP ban图，一起来玩CS内战！',
      path: '/pages/index/index'
    };
  }
});
