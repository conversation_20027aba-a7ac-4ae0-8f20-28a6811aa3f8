// app.js
App({
  globalData: {
    userInfo: null
  },
  
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        console.log(res.code)
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      },
    })

    // 初始化用户数据
    this.initUserData();
  },

  // 初始化用户数据
  initUserData() {
    // 检查是否有用户ID，没有则生成
    let userId = wx.getStorageSync('userId');
    if (!userId) {
      userId = 'user_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
      wx.setStorageSync('userId', userId);
    }

    // 初始化默认设置
    const userSettings = wx.getStorageSync('userSettings');
    if (!userSettings) {
      wx.setStorageSync('userSettings', {
        soundEnabled: true,
        vibrationEnabled: true,
        autoSave: true
      });
    }
  }
});
