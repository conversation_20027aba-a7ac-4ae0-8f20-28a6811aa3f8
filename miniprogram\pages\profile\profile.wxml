<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <image class="avatar" src="{{userInfo.avatar}}" />
    <view class="user-info">
      <text class="nickname">{{userInfo.nickname}}</text>
      <text class="user-id">ID: {{userInfo.id}}</text>
    </view>
    <button class="edit-btn" bindtap="editProfile">编辑</button>
  </view>

  <!-- 统计数据 -->
  <view class="stats-grid">
    <view class="stats-item">
      <text class="stats-number">{{stats.totalGames}}</text>
      <text class="stats-label">总场次</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{stats.totalBans}}</text>
      <text class="stats-label">BAN图次数</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{stats.totalPicks}}</text>
      <text class="stats-label">选图次数</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{stats.favoriteMap}}</text>
      <text class="stats-label">偏爱地图</text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="goToRecords">
      <view class="menu-icon">📊</view>
      <view class="menu-content">
        <text class="menu-title">历史记录</text>
        <text class="menu-desc">查看BP记录和统计</text>
      </view>
      <view class="menu-arrow">></view>
    </view>
    
    <view class="menu-item" bindtap="goToTeam">
      <view class="menu-icon">👥</view>
      <view class="menu-content">
        <text class="menu-title">分队大厅</text>
        <text class="menu-desc">加入队伍开始游戏</text>
      </view>
      <view class="menu-arrow">></view>
    </view>
    
    <view class="menu-item" bindtap="shareApp">
      <view class="menu-icon">📤</view>
      <view class="menu-content">
        <text class="menu-title">分享小程序</text>
        <text class="menu-desc">邀请好友一起玩</text>
      </view>
      <view class="menu-arrow">></view>
    </view>
    
    <view class="menu-item" bindtap="showAbout">
      <view class="menu-icon">ℹ️</view>
      <view class="menu-content">
        <text class="menu-title">关于</text>
        <text class="menu-desc">版本信息和帮助</text>
      </view>
      <view class="menu-arrow">></view>
    </view>
  </view>

  <!-- 设置选项 -->
  <view class="settings-section">
    <view class="section-title">设置</view>
    
    <view class="setting-item">
      <text class="setting-label">声音效果</text>
      <switch checked="{{settings.soundEnabled}}" bindchange="toggleSound" />
    </view>
    
    <view class="setting-item">
      <text class="setting-label">震动反馈</text>
      <switch checked="{{settings.vibrationEnabled}}" bindchange="toggleVibration" />
    </view>
    
    <view class="setting-item">
      <text class="setting-label">自动保存记录</text>
      <switch checked="{{settings.autoSave}}" bindchange="toggleAutoSave" />
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="action-btn logout-btn" bindtap="logout">退出登录</button>
    <button class="action-btn clear-btn" bindtap="clearData">清除数据</button>
  </view>
</view>
