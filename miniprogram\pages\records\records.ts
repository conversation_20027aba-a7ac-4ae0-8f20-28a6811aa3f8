// pages/records/records.ts

interface BPRecord {
  timestamp: number;
  maps: any[];
  process: any[];
  teamA: any[];
  teamB: any[];
  dateStr: string;
  timeStr: string;
  bannedMaps: string[];
  pickedMaps: string[];
}

interface FilterTab {
  label: string;
  value: string;
}

interface RecordsData {
  records: BPRecord[];
  filteredRecords: BPRecord[];
  totalRecords: number;
  todayRecords: number;
  favoriteMap: string;
  currentFilter: string;
  filterTabs: FilterTab[];
  refreshing: boolean;
}

Page<RecordsData>({
  data: {
    records: [],
    filteredRecords: [],
    totalRecords: 0,
    todayRecords: 0,
    favoriteMap: '暂无',
    currentFilter: 'all',
    filterTabs: [
      { label: '全部', value: 'all' },
      { label: '今日', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' }
    ],
    refreshing: false
  },

  onLoad() {
    this.loadRecords();
  },

  onShow() {
    this.loadRecords();
  },

  onPullDownRefresh() {
    this.loadRecords();
    wx.stopPullDownRefresh();
  },

  onRefresh() {
    this.setData({ refreshing: true });
    this.loadRecords();
    setTimeout(() => {
      this.setData({ refreshing: false });
    }, 1000);
  },

  // 加载记录
  loadRecords() {
    const records = wx.getStorageSync('bpRecords') || [];
    
    // 处理记录数据
    const processedRecords = records.map((record: any) => {
      const date = new Date(record.timestamp);
      const bannedMaps: string[] = [];
      const pickedMaps: string[] = [];
      
      record.maps.forEach((map: any) => {
        if (map.status.includes('banned')) {
          bannedMaps.push(map.name);
        } else if (map.status.includes('picked')) {
          pickedMaps.push(map.name);
        }
      });
      
      return {
        ...record,
        dateStr: this.formatDate(date),
        timeStr: this.formatTime(date),
        bannedMaps,
        pickedMaps
      };
    });

    this.setData({
      records: processedRecords,
      totalRecords: processedRecords.length
    });

    this.calculateStats(processedRecords);
    this.applyFilter();
  },

  // 计算统计信息
  calculateStats(records: BPRecord[]) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // 今日记录数
    const todayRecords = records.filter(record => {
      const recordDate = new Date(record.timestamp);
      recordDate.setHours(0, 0, 0, 0);
      return recordDate.getTime() === today.getTime();
    }).length;

    // 热门地图统计
    const mapCount: { [key: string]: number } = {};
    records.forEach(record => {
      record.pickedMaps.forEach(mapName => {
        mapCount[mapName] = (mapCount[mapName] || 0) + 1;
      });
    });

    const favoriteMap = Object.keys(mapCount).length > 0 
      ? Object.keys(mapCount).reduce((a, b) => mapCount[a] > mapCount[b] ? a : b)
      : '暂无';

    this.setData({
      todayRecords,
      favoriteMap
    });
  },

  // 切换筛选
  switchFilter(e: any) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({ currentFilter: filter });
    this.applyFilter();
  },

  // 应用筛选
  applyFilter() {
    const { records, currentFilter } = this.data;
    let filteredRecords = [...records];

    const now = new Date();
    
    switch (currentFilter) {
      case 'today':
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        filteredRecords = records.filter(record => {
          const recordDate = new Date(record.timestamp);
          recordDate.setHours(0, 0, 0, 0);
          return recordDate.getTime() === today.getTime();
        });
        break;
        
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        filteredRecords = records.filter(record => 
          record.timestamp >= weekAgo.getTime()
        );
        break;
        
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        filteredRecords = records.filter(record => 
          record.timestamp >= monthAgo.getTime()
        );
        break;
        
      default:
        // 'all' - 显示所有记录
        break;
    }

    this.setData({ filteredRecords });
  },

  // 查看记录详情
  viewRecordDetail(e: any) {
    const record = e.currentTarget.dataset.record;
    
    wx.showModal({
      title: '记录详情',
      content: `时间: ${record.dateStr} ${record.timeStr}\nA队: ${record.teamA.map((m: any) => m.nickname).join(', ')}\nB队: ${record.teamB.map((m: any) => m.nickname).join(', ')}\n被BAN: ${record.bannedMaps.join(', ')}\n选择: ${record.pickedMaps.join(', ')}`,
      showCancel: false
    });
  },

  // 分享记录
  shareRecord(e: any) {
    const record = e.currentTarget.dataset.record;
    
    const shareText = `CS内战BP记录\n时间: ${record.dateStr}\nA队 VS B队\n被BAN地图: ${record.bannedMaps.join(', ')}\n选择地图: ${record.pickedMaps.join(', ')}`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 删除记录
  deleteRecord(e: any) {
    const index = e.currentTarget.dataset.index;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      success: (res) => {
        if (res.confirm) {
          const { records } = this.data;
          records.splice(index, 1);
          
          wx.setStorageSync('bpRecords', records);
          this.loadRecords();
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 清空所有记录
  clearAllRecords() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有记录吗？此操作不可恢复！',
      confirmColor: '#e74c3c',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('bpRecords');
          this.setData({
            records: [],
            filteredRecords: [],
            totalRecords: 0,
            todayRecords: 0,
            favoriteMap: '暂无'
          });
          
          wx.showToast({
            title: '清空成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 格式化日期
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 格式化时间
  formatTime(date: Date): string {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  }
});
