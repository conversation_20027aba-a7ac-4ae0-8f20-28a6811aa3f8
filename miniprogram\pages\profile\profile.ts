// pages/profile/profile.ts

interface UserInfo {
  id: string;
  nickname: string;
  avatar: string;
}

interface UserStats {
  totalGames: number;
  totalBans: number;
  totalPicks: number;
  favoriteMap: string;
}

interface Settings {
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  autoSave: boolean;
}

interface ProfileData {
  userInfo: UserInfo;
  stats: UserStats;
  settings: Settings;
}

Page<ProfileData>({
  data: {
    userInfo: {
      id: '',
      nickname: '未登录',
      avatar: '/images/default-avatar.png'
    },
    stats: {
      totalGames: 0,
      totalBans: 0,
      totalPicks: 0,
      favoriteMap: '暂无'
    },
    settings: {
      soundEnabled: true,
      vibrationEnabled: true,
      autoSave: true
    }
  },

  onLoad() {
    this.loadUserInfo();
    this.loadUserStats();
    this.loadSettings();
  },

  onShow() {
    this.loadUserStats();
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
    }
  },

  // 加载用户统计
  loadUserStats() {
    const records = wx.getStorageSync('bpRecords') || [];
    
    let totalBans = 0;
    let totalPicks = 0;
    const mapCount: { [key: string]: number } = {};
    
    records.forEach((record: any) => {
      record.maps.forEach((map: any) => {
        if (map.status.includes('banned')) {
          totalBans++;
        } else if (map.status.includes('picked')) {
          totalPicks++;
          mapCount[map.name] = (mapCount[map.name] || 0) + 1;
        }
      });
    });

    const favoriteMap = Object.keys(mapCount).length > 0 
      ? Object.keys(mapCount).reduce((a, b) => mapCount[a] > mapCount[b] ? a : b)
      : '暂无';

    this.setData({
      stats: {
        totalGames: records.length,
        totalBans,
        totalPicks,
        favoriteMap
      }
    });
  },

  // 加载设置
  loadSettings() {
    const settings = wx.getStorageSync('userSettings') || {
      soundEnabled: true,
      vibrationEnabled: true,
      autoSave: true
    };
    this.setData({ settings });
  },

  // 编辑个人资料
  editProfile() {
    wx.getUserProfile({
      desc: '更新用户信息',
      success: (res) => {
        const userInfo = {
          ...this.data.userInfo,
          nickname: res.userInfo.nickName,
          avatar: res.userInfo.avatarUrl
        };
        
        this.setData({ userInfo });
        wx.setStorageSync('userInfo', userInfo);
        
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到记录页面
  goToRecords() {
    wx.navigateTo({
      url: '/pages/records/records'
    });
  },

  // 跳转到分队页面
  goToTeam() {
    wx.navigateTo({
      url: '/pages/team/team'
    });
  },

  // 分享小程序
  shareApp() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    wx.showToast({
      title: '点击右上角分享',
      icon: 'none'
    });
  },

  // 显示关于信息
  showAbout() {
    wx.showModal({
      title: 'CS内战分队小程序',
      content: '版本: 1.0.0\n\n功能特色:\n• 智能分队系统\n• BP ban图模式\n• 多用户实时交互\n• 详细记录统计\n\n感谢使用！',
      showCancel: false
    });
  },

  // 切换声音设置
  toggleSound(e: any) {
    const soundEnabled = e.detail.value;
    const settings = { ...this.data.settings, soundEnabled };
    
    this.setData({ settings });
    wx.setStorageSync('userSettings', settings);
  },

  // 切换震动设置
  toggleVibration(e: any) {
    const vibrationEnabled = e.detail.value;
    const settings = { ...this.data.settings, vibrationEnabled };
    
    this.setData({ settings });
    wx.setStorageSync('userSettings', settings);
  },

  // 切换自动保存设置
  toggleAutoSave(e: any) {
    const autoSave = e.detail.value;
    const settings = { ...this.data.settings, autoSave };
    
    this.setData({ settings });
    wx.setStorageSync('userSettings', settings);
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('userId');
          
          this.setData({
            userInfo: {
              id: '',
              nickname: '未登录',
              avatar: '/images/default-avatar.png'
            }
          });
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
          
          // 跳转到首页
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      }
    });
  },

  // 清除数据
  clearData() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有数据吗？包括记录、设置等，此操作不可恢复！',
      confirmColor: '#e74c3c',
      success: (res) => {
        if (res.confirm) {
          // 清除所有存储数据
          wx.removeStorageSync('bpRecords');
          wx.removeStorageSync('teamData');
          wx.removeStorageSync('userSettings');
          
          // 重置页面数据
          this.setData({
            stats: {
              totalGames: 0,
              totalBans: 0,
              totalPicks: 0,
              favoriteMap: '暂无'
            },
            settings: {
              soundEnabled: true,
              vibrationEnabled: true,
              autoSave: true
            }
          });
          
          wx.showToast({
            title: '数据已清除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'CS内战分队小程序',
      desc: '智能分队，BP ban图，一起来玩CS内战！',
      path: '/pages/index/index'
    };
  }
});
