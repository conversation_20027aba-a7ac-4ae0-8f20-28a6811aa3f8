/**index.less**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
}

.welcome-text {
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 10rpx;
  }

  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.user-avatar {
  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    border: 3rpx solid rgba(255, 255, 255, 0.3);
  }

  .default-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
  }
}

.quick-start {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
  margin-bottom: 30rpx;

  .card-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10rpx;
  }

  .card-desc {
    font-size: 26rpx;
    color: #7f8c8d;
  }
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.action-item {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  color: white;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);

  &.bp-action {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  }

  .action-icon {
    font-size: 60rpx;
    margin-bottom: 15rpx;
  }

  .action-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 8rpx;
  }

  .action-desc {
    font-size: 24rpx;
    opacity: 0.9;
  }
}

.features {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #ecf0f1;

  &:last-child {
    border-bottom: none;
  }

  .feature-icon {
    font-size: 50rpx;
    margin-right: 25rpx;
  }

  .feature-content {
    flex: 1;

    .feature-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 8rpx;
    }

    .feature-desc {
      font-size: 26rpx;
      color: #7f8c8d;
    }
  }

  .feature-arrow {
    font-size: 32rpx;
    color: #bdc3c7;
  }
}

.stats-overview {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);

  .stats-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30rpx;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;

  .stats-number {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 8rpx;
  }

  .stats-label {
    font-size: 24rpx;
    color: #7f8c8d;
  }
}
