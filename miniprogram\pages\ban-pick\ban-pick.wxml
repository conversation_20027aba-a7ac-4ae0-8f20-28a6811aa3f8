<view class="container">
  <!-- 头部信息 -->
  <view class="header">
    <view class="team-info team-a">
      <text class="team-name">A队</text>
      <text class="team-score">{{scoreA}}</text>
    </view>
    <view class="vs">VS</view>
    <view class="team-info team-b">
      <text class="team-name">B队</text>
      <text class="team-score">{{scoreB}}</text>
    </view>
  </view>

  <!-- 当前回合信息 -->
  <view class="round-info">
    <text class="round-text">{{currentRoundText}}</text>
    <view class="timer" wx:if="{{showTimer}}">
      <text>剩余时间: {{remainingTime}}s</text>
    </view>
  </view>

  <!-- 地图网格 -->
  <view class="maps-grid">
    <view 
      wx:for="{{maps}}" 
      wx:key="id" 
      class="map-item {{item.status}}"
      bindtap="handleMapClick"
      data-map-id="{{item.id}}"
    >
      <image class="map-image" src="{{item.image}}" />
      <view class="map-name">{{item.name}}</view>
      <view class="map-status-overlay" wx:if="{{item.status !== 'available'}}">
        <text wx:if="{{item.status === 'banned-a'}}" class="status-text banned-a">A队BAN</text>
        <text wx:if="{{item.status === 'banned-b'}}" class="status-text banned-b">B队BAN</text>
        <text wx:if="{{item.status === 'picked-a'}}" class="status-text picked-a">A队选择</text>
        <text wx:if="{{item.status === 'picked-b'}}" class="status-text picked-b">B队选择</text>
      </view>
    </view>
  </view>

  <!-- BP流程显示 -->
  <view class="bp-process">
    <text class="process-title">BP流程</text>
    <scroll-view class="process-list" scroll-y>
      <view wx:for="{{bpProcess}}" wx:key="index" class="process-item {{item.completed ? 'completed' : ''}} {{item.current ? 'current' : ''}}">
        <view class="step-number">{{index + 1}}</view>
        <view class="step-content">
          <text class="step-text">{{item.text}}</text>
          <text wx:if="{{item.mapName}}" class="step-map">- {{item.mapName}}</text>
        </view>
        <view wx:if="{{item.completed}}" class="step-check">✓</view>
      </view>
    </scroll-view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn reset-btn" bindtap="resetBP">重置BP</button>
    <button class="action-btn finish-btn" bindtap="finishBP" disabled="{{!canFinish}}">
      完成BP
    </button>
  </view>
</view>
