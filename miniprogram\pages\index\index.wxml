<!--index.wxml-->
<navigation-bar title="CS内战分队" back="{{false}}" color="white" background="#667eea"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 头部欢迎区域 -->
    <view class="header">
      <view class="welcome-text">
        <text class="title">CS内战分队</text>
        <text class="subtitle">专业的CS:GO内战分队工具</text>
      </view>
      <view class="user-avatar" bindtap="goToProfile">
        <image wx:if="{{userInfo.avatarUrl}}" class="avatar" src="{{userInfo.avatarUrl}}" />
        <view wx:else class="default-avatar">👤</view>
      </view>
    </view>

    <!-- 快速开始卡片 -->
    <view class="quick-start">
      <view class="card-header">
        <text class="card-title">快速开始</text>
        <text class="card-desc">选择你想要的功能</text>
      </view>

      <view class="action-grid">
        <view class="action-item team-action" bindtap="goToTeam">
          <view class="action-icon">👥</view>
          <text class="action-title">分队</text>
          <text class="action-desc">A队 VS B队</text>
        </view>

        <view class="action-item bp-action" bindtap="goToBanPick">
          <view class="action-icon">🗺️</view>
          <text class="action-title">BP Ban图</text>
          <text class="action-desc">7张地图选择</text>
        </view>
      </view>
    </view>

    <!-- 功能卡片 -->
    <view class="features">
      <view class="feature-item" bindtap="goToRecords">
        <view class="feature-icon">📊</view>
        <view class="feature-content">
          <text class="feature-title">历史记录</text>
          <text class="feature-desc">查看BP记录和统计数据</text>
        </view>
        <view class="feature-arrow">></view>
      </view>

      <view class="feature-item" bindtap="goToProfile">
        <view class="feature-icon">⚙️</view>
        <view class="feature-content">
          <text class="feature-title">个人中心</text>
          <text class="feature-desc">设置和个人信息管理</text>
        </view>
        <view class="feature-arrow">></view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-overview">
      <text class="stats-title">今日统计</text>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-number">{{todayStats.games}}</text>
          <text class="stats-label">场次</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{todayStats.users}}</text>
          <text class="stats-label">用户</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{todayStats.maps}}</text>
          <text class="stats-label">地图</text>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
