.container {
  padding: 20rpx;
  min-height: 100vh;
  background: #f8f9fa;
}

.user-card {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-right: 30rpx;
  }
  
  .user-info {
    flex: 1;
    
    .nickname {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 10rpx;
    }
    
    .user-id {
      font-size: 24rpx;
      color: #7f8c8d;
    }
  }
  
  .edit-btn {
    background: #3498db;
    color: white;
    border-radius: 25rpx;
    padding: 15rpx 30rpx;
    font-size: 26rpx;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stats-item {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  
  .stats-number {
    display: block;
    font-size: 42rpx;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 10rpx;
  }
  
  .stats-label {
    font-size: 24rpx;
    color: #7f8c8d;
  }
}

.menu-section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #ecf0f1;
  
  &:last-child {
    border-bottom: none;
  }
  
  .menu-icon {
    font-size: 40rpx;
    margin-right: 25rpx;
  }
  
  .menu-content {
    flex: 1;
    
    .menu-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 5rpx;
    }
    
    .menu-desc {
      font-size: 24rpx;
      color: #7f8c8d;
    }
  }
  
  .menu-arrow {
    font-size: 32rpx;
    color: #bdc3c7;
  }
}

.settings-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 25rpx;
  }
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
  
  &:last-child {
    border-bottom: none;
  }
  
  .setting-label {
    font-size: 28rpx;
    color: #2c3e50;
  }
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  
  &.logout-btn {
    background: #95a5a6;
    color: white;
  }
  
  &.clear-btn {
    background: #e74c3c;
    color: white;
  }
}
