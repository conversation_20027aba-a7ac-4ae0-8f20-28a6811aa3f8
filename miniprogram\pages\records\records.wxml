<view class="container">
  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-card">
      <text class="stats-number">{{totalRecords}}</text>
      <text class="stats-label">总场次</text>
    </view>
    <view class="stats-card">
      <text class="stats-number">{{todayRecords}}</text>
      <text class="stats-label">今日场次</text>
    </view>
    <view class="stats-card">
      <text class="stats-number">{{favoriteMap}}</text>
      <text class="stats-label">热门地图</text>
    </view>
  </view>

  <!-- 筛选选项 -->
  <view class="filter-section">
    <view class="filter-tabs">
      <view 
        wx:for="{{filterTabs}}" 
        wx:key="value"
        class="filter-tab {{currentFilter === item.value ? 'active' : ''}}"
        bindtap="switchFilter"
        data-filter="{{item.value}}"
      >
        {{item.label}}
      </view>
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="records-section">
    <view wx:if="{{filteredRecords.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/empty.png" />
      <text class="empty-text">暂无记录</text>
    </view>
    
    <scroll-view wx:else class="records-list" scroll-y refresher-enabled bindrefresherrefresh="onRefresh" refresher-triggered="{{refreshing}}">
      <view wx:for="{{filteredRecords}}" wx:key="timestamp" class="record-item" bindtap="viewRecordDetail" data-record="{{item}}">
        <!-- 记录头部 -->
        <view class="record-header">
          <view class="record-time">
            <text class="date">{{item.dateStr}}</text>
            <text class="time">{{item.timeStr}}</text>
          </view>
          <view class="record-type">
            <text>BP记录</text>
          </view>
        </view>

        <!-- 队伍信息 -->
        <view class="teams-info">
          <view class="team-info">
            <text class="team-name">A队 ({{item.teamA.length}}人)</text>
            <view class="team-members">
              <text wx:for="{{item.teamA}}" wx:key="id" wx:for-item="member" class="member-name">
                {{member.nickname}}
              </text>
            </view>
          </view>
          <view class="vs-text">VS</view>
          <view class="team-info">
            <text class="team-name">B队 ({{item.teamB.length}}人)</text>
            <view class="team-members">
              <text wx:for="{{item.teamB}}" wx:key="id" wx:for-item="member" class="member-name">
                {{member.nickname}}
              </text>
            </view>
          </view>
        </view>

        <!-- 地图结果 -->
        <view class="maps-result">
          <view class="result-section">
            <text class="section-title">被BAN地图</text>
            <view class="maps-list banned">
              <text wx:for="{{item.bannedMaps}}" wx:key="*this" class="map-name banned">{{item}}</text>
            </view>
          </view>
          <view class="result-section">
            <text class="section-title">选择地图</text>
            <view class="maps-list picked">
              <text wx:for="{{item.pickedMaps}}" wx:key="*this" class="map-name picked">{{item}}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="record-actions">
          <view class="action-btn share-btn" bindtap="shareRecord" data-record="{{item}}" catchtap="true">
            <text>分享</text>
          </view>
          <view class="action-btn delete-btn" bindtap="deleteRecord" data-index="{{index}}" catchtap="true">
            <text>删除</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 清空记录按钮 -->
  <view class="bottom-actions" wx:if="{{totalRecords > 0}}">
    <button class="clear-btn" bindtap="clearAllRecords">清空所有记录</button>
  </view>
</view>
