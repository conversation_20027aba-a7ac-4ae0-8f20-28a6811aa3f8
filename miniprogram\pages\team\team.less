.container {
  padding: 20rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.teams-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40rpx;
  gap: 20rpx;
}

.team-section {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.team-a {
  border-left: 8rpx solid #ff6b6b;
}

.team-b {
  border-left: 8rpx solid #4ecdc4;
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .team-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .team-count {
    font-size: 24rpx;
    color: #666;
    background: #f0f0f0;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
  }
}

.team-members {
  min-height: 300rpx;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  
  .avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  
  .nickname {
    flex: 1;
    font-size: 28rpx;
    color: #333;
  }
  
  .leave-btn {
    background: #ff4757;
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
  }
}

.join-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  background: #ddd;
  border: 2rpx dashed #999;
  border-radius: 10rpx;
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  margin-top: 100rpx;
  
  text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.reset-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.start-btn {
  background: #2ed573;
  color: white;
}

.start-btn[disabled] {
  background: #ccc;
  color: #999;
}

.online-users {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .users-list {
    max-height: 300rpx;
  }
  
  .user-item {
    display: flex;
    align-items: center;
    padding: 15rpx 0;
    border-bottom: 1rpx solid #eee;
    
    .avatar {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      margin-right: 15rpx;
    }
    
    .nickname {
      flex: 1;
      font-size: 26rpx;
      color: #333;
    }
    
    .status {
      font-size: 24rpx;
      color: #666;
      background: #f0f0f0;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
    }
  }
}
