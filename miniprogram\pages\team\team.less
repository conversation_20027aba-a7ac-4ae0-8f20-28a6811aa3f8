.container {
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }
}

.header {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  z-index: 1;

  .title {
    display: block;
    font-size: 56rpx;
    font-weight: 800;
    color: white;
    margin-bottom: 16rpx;
    text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
    letter-spacing: 2rpx;
  }

  .subtitle {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }
}

.teams-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 60rpx;
  gap: 40rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 1;
}

.team-section {
  width: 280rpx;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, transparent, currentColor, transparent);
  }
}

.team-a {
  color: #ff4757;

  &::before {
    background: linear-gradient(90deg, #ff6b6b, #ff4757, #ff3742);
  }

  .team-header .team-name {
    color: #ff4757;
  }
}

.team-b {
  color: #3742fa;

  &::before {
    background: linear-gradient(90deg, #70a1ff, #3742fa, #2f3542);
  }

  .team-header .team-name {
    color: #3742fa;
  }
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;

  .team-name {
    font-size: 42rpx;
    font-weight: 800;
    letter-spacing: 1rpx;
  }

  .team-count {
    font-size: 26rpx;
    color: #666;
    background: rgba(0, 0, 0, 0.05);
    padding: 12rpx 20rpx;
    border-radius: 25rpx;
    font-weight: 600;
    min-width: 60rpx;
    text-align: center;
  }
}

.team-members {
  min-height: 400rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-2rpx);
  }

  .avatar {
    width: 70rpx;
    height: 70rpx;
    border-radius: 50%;
    margin-right: 24rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .nickname {
    flex: 1;
    font-size: 30rpx;
    color: #2c3e50;
    font-weight: 600;
  }

  .leave-btn {
    background: linear-gradient(135deg, #ff6b6b, #ff4757);
    color: white;
    padding: 12rpx 20rpx;
    border-radius: 25rpx;
    font-size: 24rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }
  }
}

.join-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 2rpx dashed rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  margin-top: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 32rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  }
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #ff6b6b, #3742fa);
  border-radius: 50%;
  margin-top: 120rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -6rpx;
    left: -6rpx;
    right: -6rpx;
    bottom: -6rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b, #3742fa);
    z-index: -1;
    filter: blur(8rpx);
    opacity: 0.6;
  }

  text {
    font-size: 36rpx;
    font-weight: 900;
    color: white;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    letter-spacing: 2rpx;
  }
}

.actions {
  display: flex;
  gap: 30rpx;
  margin: 60rpx 40rpx 40rpx;
  position: relative;
  z-index: 1;
}

.action-btn {
  flex: 1;
  height: 100rpx;
  border-radius: 50rpx;
  font-size: 34rpx;
  font-weight: 700;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  }
}

.reset-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
}

.start-btn {
  background: linear-gradient(135deg, #00d2ff, #3a7bd5);
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.start-btn[disabled] {
  background: linear-gradient(135deg, #bdc3c7, #95a5a6);
  color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.online-users {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 0 30rpx 40rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;

  .section-title {
    display: block;
    font-size: 36rpx;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 30rpx;
    text-align: center;
    letter-spacing: 1rpx;
  }

  .users-list {
    max-height: 400rpx;
  }

  .user-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-radius: 16rpx;
    margin-bottom: 12rpx;
    background: rgba(0, 0, 0, 0.02);
    border: 1rpx solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
      transform: translateX(4rpx);
    }

    .avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      margin-right: 20rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.8);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    }

    .nickname {
      flex: 1;
      font-size: 28rpx;
      color: #2c3e50;
      font-weight: 600;
    }

    .status {
      font-size: 24rpx;
      color: #666;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-weight: 600;
      border: 1rpx solid rgba(0, 0, 0, 0.05);
    }
  }
}
