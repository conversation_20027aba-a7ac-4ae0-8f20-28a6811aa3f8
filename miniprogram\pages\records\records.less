.container {
  padding: 20rpx;
  min-height: 100vh;
  background: #f5f5f5;
}

.stats-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stats-card {
  flex: 1;
  background: white;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  
  .stats-number {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10rpx;
  }
  
  .stats-label {
    font-size: 24rpx;
    color: #7f8c8d;
  }
}

.filter-section {
  margin-bottom: 30rpx;
}

.filter-tabs {
  display: flex;
  background: white;
  border-radius: 15rpx;
  padding: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #7f8c8d;
  transition: all 0.3s ease;
  
  &.active {
    background: #3498db;
    color: white;
    font-weight: bold;
  }
}

.records-section {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  
  .empty-icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 32rpx;
    color: #bdc3c7;
  }
}

.records-list {
  height: calc(100vh - 400rpx);
}

.record-item {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #ecf0f1;
  
  .record-time {
    .date {
      font-size: 32rpx;
      font-weight: bold;
      color: #2c3e50;
      margin-right: 15rpx;
    }
    
    .time {
      font-size: 24rpx;
      color: #7f8c8d;
    }
  }
  
  .record-type {
    background: #e8f4fd;
    color: #3498db;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
  }
}

.teams-info {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  gap: 20rpx;
}

.team-info {
  flex: 1;
  
  .team-name {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10rpx;
  }
  
  .team-members {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    
    .member-name {
      background: #ecf0f1;
      color: #7f8c8d;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
      font-size: 22rpx;
    }
  }
}

.vs-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #e74c3c;
}

.maps-result {
  margin-bottom: 25rpx;
}

.result-section {
  margin-bottom: 15rpx;
  
  .section-title {
    display: block;
    font-size: 26rpx;
    font-weight: bold;
    color: #34495e;
    margin-bottom: 10rpx;
  }
  
  .maps-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    
    .map-name {
      padding: 8rpx 16rpx;
      border-radius: 15rpx;
      font-size: 24rpx;
      
      &.banned {
        background: #fadbd8;
        color: #e74c3c;
      }
      
      &.picked {
        background: #d5f4e6;
        color: #27ae60;
      }
    }
  }
}

.record-actions {
  display: flex;
  gap: 15rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  
  &.share-btn {
    background: #e8f4fd;
    color: #3498db;
  }
  
  &.delete-btn {
    background: #fadbd8;
    color: #e74c3c;
  }
}

.bottom-actions {
  margin-top: 30rpx;
  text-align: center;
}

.clear-btn {
  background: #e74c3c;
  color: white;
  border-radius: 25rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}
