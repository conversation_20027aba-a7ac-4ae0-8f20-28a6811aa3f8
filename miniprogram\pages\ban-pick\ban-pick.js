// pages/ban-pick/ban-pick.js

Page({
  data: {
    maps: [
      { id: 'dust2', name: '沙二', image: 'https://via.placeholder.com/400x300/8B4513/FFFFFF?text=沙二', status: 'available' },
      { id: 'mirage', name: '小镇', image: 'https://via.placeholder.com/400x300/228B22/FFFFFF?text=小镇', status: 'available' },
      { id: 'inferno', name: '迷城', image: 'https://via.placeholder.com/400x300/FF4500/FFFFFF?text=迷城', status: 'available' },
      { id: 'train', name: '火车', image: 'https://via.placeholder.com/400x300/708090/FFFFFF?text=火车', status: 'available' },
      { id: 'ancient', name: '遗迹', image: 'https://via.placeholder.com/400x300/8B4513/FFFFFF?text=遗迹', status: 'available' },
      { id: 'nuke', name: '核子', image: 'https://via.placeholder.com/400x300/32CD32/FFFFFF?text=核子', status: 'available' },
      { id: 'vertigo', name: '游乐园', image: 'https://via.placeholder.com/400x300/FF69B4/FFFFFF?text=游乐园', status: 'available' }
    ],
    bpProcess: [
      { text: 'A队 BAN 一张地图', team: 'A', action: 'ban', completed: false, current: true },
      { text: 'B队 BAN 一张地图', team: 'B', action: 'ban', completed: false, current: false },
      { text: 'A队 选择一张地图', team: 'A', action: 'pick', completed: false, current: false },
      { text: 'B队 选择一张地图', team: 'B', action: 'pick', completed: false, current: false },
      { text: 'A队 BAN 一张地图', team: 'A', action: 'ban', completed: false, current: false },
      { text: 'B队 BAN 一张地图', team: 'B', action: 'ban', completed: false, current: false },
      { text: '最后一张地图自动选择', team: 'A', action: 'pick', completed: false, current: false }
    ],
    currentStep: 0,
    currentRoundText: 'A队 BAN 一张地图',
    remainingTime: 30,
    showTimer: true,
    scoreA: 0,
    scoreB: 0,
    canFinish: false
  },

  timer: null,

  onLoad() {
    this.loadTeamData();
    this.startTimer();
  },

  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },

  // 加载队伍数据
  loadTeamData() {
    const teamData = wx.getStorageSync('currentTeams');
    if (teamData) {
      console.log('Team data loaded:', teamData);
    }
  },

  // 开始计时器
  startTimer() {
    this.timer = setInterval(() => {
      const { remainingTime } = this.data;
      if (remainingTime > 0) {
        this.setData({
          remainingTime: remainingTime - 1
        });
      } else {
        this.autoSkipStep();
      }
    }, 1000);
  },

  // 自动跳过步骤
  autoSkipStep() {
    wx.showToast({
      title: '时间到，自动跳过',
      icon: 'none'
    });
    this.nextStep();
  },

  // 处理地图点击
  handleMapClick(e) {
    const mapId = e.currentTarget.dataset.mapId;
    const { maps, currentStep, bpProcess } = this.data;

    const map = maps.find(m => m.id === mapId);
    const currentBPStep = bpProcess[currentStep];

    if (!map || map.status !== 'available' || !currentBPStep) {
      // 震动反馈
      wx.vibrateShort();
      wx.showToast({
        title: '无法选择此地图',
        icon: 'none'
      });
      return;
    }

    // 成功选择的反馈
    wx.vibrateShort();
    if (currentBPStep.action === 'ban') {
      wx.showToast({
        title: `${currentBPStep.team}队 BAN 了 ${map.name}`,
        icon: 'none'
      });
    } else {
      wx.showToast({
        title: `${currentBPStep.team}队 选择了 ${map.name}`,
        icon: 'success'
      });
    }

    // 更新地图状态
    const newMaps = maps.map(m => {
      if (m.id === mapId) {
        if (currentBPStep.action === 'ban') {
          m.status = currentBPStep.team === 'A' ? 'banned-a' : 'banned-b';
        } else {
          m.status = currentBPStep.team === 'A' ? 'picked-a' : 'picked-b';
        }
      }
      return m;
    });

    // 更新BP流程
    const newBPProcess = bpProcess.map((step, index) => {
      if (index === currentStep) {
        step.completed = true;
        step.current = false;
        step.mapName = map.name;
      } else if (index === currentStep + 1) {
        step.current = true;
      }
      return step;
    });

    this.setData({
      maps: newMaps,
      bpProcess: newBPProcess
    });

    this.nextStep();
  },

  // 进入下一步
  nextStep() {
    const { currentStep, bpProcess } = this.data;
    const nextStep = currentStep + 1;

    if (nextStep >= bpProcess.length) {
      // BP完成，处理最后一张地图
      this.handleFinalMap();
      return;
    }

    const nextBPStep = bpProcess[nextStep];
    this.setData({
      currentStep: nextStep,
      currentRoundText: nextBPStep.text,
      remainingTime: 30
    });
  },

  // 处理最后一张地图
  handleFinalMap() {
    const { maps } = this.data;
    const finalMap = maps.find(m => m.status === 'available');
    
    if (finalMap) {
      const newMaps = maps.map(m => {
        if (m.id === finalMap.id) {
          m.status = 'picked-a'; // 默认给A队
        }
        return m;
      });

      const newBPProcess = this.data.bpProcess.map((step, index) => {
        if (index === this.data.bpProcess.length - 1) {
          step.completed = true;
          step.current = false;
          step.mapName = finalMap.name;
        }
        return step;
      });

      this.setData({
        maps: newMaps,
        bpProcess: newBPProcess,
        currentRoundText: 'BP完成！',
        showTimer: false,
        canFinish: true
      });

      if (this.timer) {
        clearInterval(this.timer);
      }
    }
  },

  // 重置BP
  resetBP() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置BP流程吗？',
      success: (res) => {
        if (res.confirm) {
          // 重置所有数据
          const resetMaps = this.data.maps.map(m => ({
            ...m,
            status: 'available'
          }));

          const resetBPProcess = this.data.bpProcess.map((step, index) => ({
            ...step,
            completed: false,
            current: index === 0,
            mapName: undefined
          }));

          this.setData({
            maps: resetMaps,
            bpProcess: resetBPProcess,
            currentStep: 0,
            currentRoundText: 'A队 BAN 一张地图',
            remainingTime: 30,
            showTimer: true,
            canFinish: false
          });

          this.startTimer();
        }
      }
    });
  },

  // 完成BP
  finishBP() {
    const { maps, bpProcess } = this.data;
    
    // 保存BP记录
    const bpRecord = {
      timestamp: Date.now(),
      maps: maps,
      process: bpProcess,
      teamA: wx.getStorageSync('currentTeams')?.teamA || [],
      teamB: wx.getStorageSync('currentTeams')?.teamB || []
    };

    // 获取历史记录
    const records = wx.getStorageSync('bpRecords') || [];
    records.unshift(bpRecord);
    
    // 只保留最近50条记录
    if (records.length > 50) {
      records.splice(50);
    }
    
    wx.setStorageSync('bpRecords', records);

    wx.showToast({
      title: 'BP完成，已保存记录',
      icon: 'success'
    });

    // 跳转到记录页面
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/records/records'
      });
    }, 1500);
  }
});
