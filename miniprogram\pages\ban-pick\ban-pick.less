.container {
  padding: 20rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
}

.team-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .team-name {
    font-size: 32rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 10rpx;
  }
  
  .team-score {
    font-size: 48rpx;
    font-weight: bold;
    color: #f39c12;
  }
}

.team-a .team-name {
  color: #e74c3c;
}

.team-b .team-name {
  color: #3498db;
}

.vs {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.round-info {
  text-align: center;
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 20rpx;
  
  .round-text {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 10rpx;
  }
  
  .timer {
    font-size: 24rpx;
    color: #f39c12;
  }
}

.maps-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.map-item {
  position: relative;
  background: white;
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  
  &.available {
    transform: scale(1);
    opacity: 1;
  }
  
  &.banned-a,
  &.banned-b {
    opacity: 0.5;
    transform: scale(0.95);
  }
  
  &.picked-a,
  &.picked-b {
    transform: scale(1.05);
    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.3);
  }
}

.map-image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.map-name {
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.map-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  
  .status-text {
    font-size: 24rpx;
    font-weight: bold;
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    
    &.banned-a,
    &.banned-b {
      background: #e74c3c;
      color: white;
    }
    
    &.picked-a {
      background: #e74c3c;
      color: white;
    }
    
    &.picked-b {
      background: #3498db;
      color: white;
    }
  }
}

.bp-process {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .process-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 20rpx;
    text-align: center;
  }
  
  .process-list {
    max-height: 300rpx;
  }
}

.process-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  
  &.completed {
    opacity: 0.6;
  }
  
  &.current {
    background: rgba(241, 196, 15, 0.2);
    border-radius: 10rpx;
    padding: 15rpx;
    margin: 5rpx 0;
  }
  
  .step-number {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: white;
    margin-right: 20rpx;
  }
  
  .step-content {
    flex: 1;
    
    .step-text {
      display: block;
      font-size: 26rpx;
      color: white;
    }
    
    .step-map {
      font-size: 22rpx;
      color: #f39c12;
    }
  }
  
  .step-check {
    color: #2ecc71;
    font-size: 32rpx;
    font-weight: bold;
  }
}

.actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.reset-btn {
  background: rgba(231, 76, 60, 0.8);
  color: white;
}

.finish-btn {
  background: rgba(46, 204, 113, 0.8);
  color: white;
}

.finish-btn[disabled] {
  background: rgba(149, 165, 166, 0.5);
  color: rgba(255, 255, 255, 0.5);
}
